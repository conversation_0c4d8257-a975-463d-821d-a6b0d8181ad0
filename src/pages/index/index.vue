<script setup>
const appStore = useAppStore()
const router = useRouter()

// 轮播图数据
const bannerList = ref([
  {
    id: 1,
    image: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=400&fit=crop',
    title: '智慧公寓管理系统',
    subtitle: '为您提供便捷的公寓生活服务',
    action: '立即体验',
  },
  {
    id: 2,
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=400&fit=crop',
    title: '在线缴费服务',
    subtitle: '足不出户，轻松缴纳各项费用',
    action: '去缴费',
  },
  {
    id: 3,
    image: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=400&fit=crop',
    title: '24小时服务热线',
    subtitle: '随时为您解决生活中的问题',
    action: '联系我们',
  },
])

const currentBanner = ref(0)

// 快捷功能数据
const quickActions = ref([
  {
    id: 1,
    icon: 'i-carbon-home',
    title: '入住办理',
    color: 'from-blue-400 to-blue-600',
    path: '/occupancy',
  },
  {
    id: 2,
    icon: 'i-carbon-wallet',
    title: '在线缴费',
    color: 'from-green-400 to-green-600',
    path: '/payment',
  },
  {
    id: 3,
    icon: 'i-carbon-document',
    title: '租赁信息',
    color: 'from-purple-400 to-purple-600',
    path: '/rental-info',
  },
  {
    id: 4,
    icon: 'i-carbon-chat',
    title: '投诉建议',
    color: 'from-orange-400 to-orange-600',
    path: '/contact',
  },
])

// 公告通知数据
const announcements = ref([
  {
    id: 1,
    title: '关于公寓电梯维护的通知',
    content: '为确保电梯安全运行，将于本周末进行例行维护...',
    time: '2024-01-15',
    isNew: true,
  },
  {
    id: 2,
    title: '春节期间物业服务安排',
    content: '春节期间物业服务时间调整，详情请查看...',
    time: '2024-01-10',
    isNew: false,
  },
  {
    id: 3,
    title: '公寓WiFi升级完成通知',
    content: '公寓WiFi网络已完成升级，网速大幅提升...',
    time: '2024-01-08',
    isNew: false,
  },
])

// 公寓服务数据
const apartmentServices = ref([
  {
    id: 1,
    icon: 'i-carbon-tools',
    title: '物业服务',
    description: '日常维护、清洁服务',
    contact: '400-123-4567',
    time: '周一至周日 8:00-18:00',
  },
  {
    id: 2,
    icon: 'i-carbon-warning',
    title: '紧急维修',
    description: '24小时紧急维修服务',
    contact: '400-999-8888',
    time: '24小时服务',
  },
  {
    id: 3,
    icon: 'i-carbon-time',
    title: '服务时间',
    description: '前台咨询服务时间',
    contact: '400-123-4567',
    time: '周一至周日 9:00-21:00',
  },
])

// 消息未读数量
const unreadCount = ref(3)

// 轮播图切换
function onBannerChange(e) {
  currentBanner.value = e.detail.current
}

// 快捷功能点击
function handleQuickAction(item) {
  uni.vibrateShort?.()
  router.push({ path: item.path })
}

// 搜索按钮点击
function handleSearch() {
  uni.showToast({
    title: '搜索功能开发中',
    icon: 'none',
  })
}

// 消息通知点击
function handleNotification() {
  router.push({ path: '/notifications' })
}

// 公告点击
function handleAnnouncementClick(item) {
  router.push({
    path: '/announcement-detail',
    query: { id: item.id },
  })
}

// 查看更多公告
function handleMoreAnnouncements() {
  router.push({ path: '/announcements' })
}

// 拨打电话
function handleCall(phone) {
  uni.makePhoneCall({
    phoneNumber: phone,
  })
}

// 复制电话号码
function handleCopyPhone(phone) {
  uni.setClipboardData({
    data: phone,
    success: () => {
      uni.showToast({
        title: '电话号码已复制',
        icon: 'success',
      })
    },
  })
}
</script>

<template>
  <view class="h-full flex flex-col">
    <!-- 顶部安全区域 -->
    <view class="h-[--safe-top] bg-primary-500 flex-none"></view>

    <!-- 顶部导航 -->
    <view class="flex items-center justify-between bg-primary-500 pl-4 pb-3 pt-6 pr-[--safe-right]">
      <!-- 左侧：Logo + 标题 -->
      <view class="flex items-center">
        <image
          src="~@assets/images/avatar.gif"
          class="h-8 w-8 rounded-full mr-3"
          mode="aspectFill"
        />
        <view class="text-white">
          <view class="text-base font-bold">
            人才智慧公寓
          </view>
        </view>
      </view>

      <!-- 右侧：消息通知 + 搜索 -->
      <view class="flex items-center space-x-3">
        <!-- 搜索按钮 -->
        <!-- <view
          class="size-7 flex items-center justify-center rounded-full bg-white/10 active:bg-white/20 transition-colors"
          @click="handleSearch"
        >
          <view class="i-carbon-search w-4 h-4 text-white"></view>
        </view> -->

        <!-- 消息通知 -->
        <view
          class="relative size-7 flex items-center justify-center rounded-full bg-white/10 active:bg-white/20 transition-colors"
          @click="handleNotification"
        >
          <view class="i-carbon-notification w-4 h-4 text-white"></view>
          <view
            v-if="unreadCount > 0"
            class="absolute -top-2 -right-2 min-w-5 h-5 bg-red-500 rounded-full flex items-center justify-center"
          >
            <text class="text-xs text-white font-bold">
              {{ unreadCount > 99 ? '99+' : unreadCount }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <view class="flex-1 h-0 overflow-auto">
      <!-- 轮播图 Banner -->
      <view class="px-4 pt-4">
        <swiper
          class="h-40 rounded-xl overflow-hidden"
          :indicator-dots="true"
          :autoplay="true"
          :interval="3000"
          :duration="500"
          circular
          indicator-color="rgba(255,255,255,0.5)"
          indicator-active-color="#ffffff"
          @change="onBannerChange"
        >
          <swiper-item
            v-for="banner in bannerList"
            :key="banner.id"
            class="relative"
          >
            <view class="relative h-full w-full overflow-hidden rounded-xl">
              <image
                :src="banner.image"
                mode="aspectFill"
                class="h-full w-full object-cover"
              />
              <view class="absolute inset-0 bg-gradient-to-r from-black/40 to-transparent"></view>
              <view class="absolute bottom-0 left-0 p-4 text-white">
                <view class="text-lg font-bold mb-1">
                  {{ banner.title }}
                </view>
                <view class="text-sm text-white/90 mb-3">
                  {{ banner.subtitle }}
                </view>
                <view class="inline-block bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-xs">
                  {{ banner.action }}
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 快捷功能入口 -->
      <view class="px-4 pt-6">
        <view class="mb-4">
          <view class="text-lg font-bold text-gray-800">
            快捷服务
          </view>
          <view class="text-sm text-gray-500">
            常用功能，一键直达
          </view>
        </view>

        <view class="grid grid-cols-2 gap-4">
          <view
            v-for="action in quickActions"
            :key="action.id"
            class="flex items-center p-4 bg-white rounded-xl shadow-sm active:scale-95 transition-transform space-x-2"
            @click="handleQuickAction(action)"
          >
            <view
              class="size-9 rounded-full flex items-center justify-center bg-gradient-to-r"
              :class="action.color"
            >
              <view :class="`${action.icon} size-6 text-white`"></view>
            </view>
            <view class="text-sm text-gray-700 font-medium text-center">
              {{ action.title }}
            </view>
          </view>
        </view>
      </view>

      <!-- 公告通知 -->
      <view class="px-4 pt-6">
        <view class="flex items-center justify-between mb-4">
          <view>
            <view class="text-lg font-bold text-gray-800">
              公告通知
            </view>
            <view class="text-sm text-gray-500">
              最新资讯，及时了解
            </view>
          </view>
          <view
            class="flex items-center text-primary-500 text-sm active:text-primary-700"
            @click="handleMoreAnnouncements"
          >
            <text class="mr-1">
              更多
            </text>
            <view class="i-carbon-chevron-right w-4 h-4"></view>
          </view>
        </view>

        <view class="bg-white rounded-xl shadow-sm overflow-hidden">
          <view
            v-for="(announcement, index) in announcements.slice(0, 3)"
            :key="announcement.id"
            class="flex items-center px-4 py-4 active:bg-gray-50 transition-colors"
            :class="[index !== announcements.slice(0, 3).length - 1 ? 'border-b border-gray-100' : '']"
            @click="handleAnnouncementClick(announcement)"
          >
            <view class="flex-1 min-w-0">
              <view class="flex items-center mb-1">
                <text class="text-gray-800 font-medium text-sm truncate">
                  {{ announcement.title }}
                </text>
                <view
                  v-if="announcement.isNew"
                  class="ml-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full"
                >
                  新
                </view>
              </view>
              <view class="text-gray-500 text-xs line-clamp-2 mb-2">
                {{ announcement.content }}
              </view>
              <view class="text-gray-400 text-xs">
                {{ announcement.time }}
              </view>
            </view>
            <view class="ml-3 text-gray-400">
              <view class="i-carbon-chevron-right w-4 h-4"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 公寓服务 -->
      <view class="px-4 pt-6 pb-6">
        <view class="mb-4">
          <view class="text-lg font-bold text-gray-800">
            公寓服务
          </view>
          <view class="text-sm text-gray-500">
            贴心服务，随时联系
          </view>
        </view>

        <view class="space-y-3">
          <view
            v-for="service in apartmentServices"
            :key="service.id"
            class="bg-white rounded-xl shadow-sm p-4"
          >
            <view class="flex items-start">
              <view class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center mr-3 flex-shrink-0">
                <view :class="`${service.icon} w-5 h-5 text-primary-500`"></view>
              </view>

              <view class="flex-1 min-w-0">
                <view class="flex items-center justify-between mb-2">
                  <view class="text-gray-800 font-medium">
                    {{ service.title }}
                  </view>
                  <view class="flex items-center space-x-2">
                    <!-- 拨打电话 -->
                    <view
                      class="p-2 rounded-full bg-green-100 active:bg-green-200 transition-colors"
                      @click="handleCall(service.contact)"
                    >
                      <view class="i-carbon-phone w-4 h-4 text-green-600"></view>
                    </view>
                    <!-- 复制电话 -->
                    <view
                      class="p-2 rounded-full bg-gray-100 active:bg-gray-200 transition-colors"
                      @click="handleCopyPhone(service.contact)"
                    >
                      <view class="i-carbon-copy w-4 h-4 text-gray-600"></view>
                    </view>
                  </view>
                </view>

                <view class="text-gray-500 text-sm mb-2">
                  {{ service.description }}
                </view>

                <view class="flex items-center justify-between text-xs">
                  <view class="flex items-center text-gray-600">
                    <view class="i-carbon-phone w-3 h-3 mr-1"></view>
                    <text>{{ service.contact }}</text>
                  </view>
                  <view class="flex items-center text-gray-500">
                    <view class="i-carbon-time w-3 h-3 mr-1"></view>
                    <text>{{ service.time }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
</style>

<script setup>
import { showToast } from '@uni-helper/uni-promises'
import ApplicationForm from './components/ApplicationForm.vue'
import ApplicationProgress from './components/ApplicationProgress.vue'

// 标签数据
const tabModel = [
  {
    label: '入住申请',
  },
  {
    label: '申请进度',
  },
]
const tabIndex = ref(0)

// 申请进度数据
const applicationProgress = ref({
  applicationId: 'AP202501080001',
  status: 'reviewing', // submitted, reviewing, contract, completed, rejected
  submitTime: '2025-01-08 14:30:00',
  steps: [
    { key: 'submitted', label: '申请提交', status: 'completed', time: '2025-01-08 14:30:00' },
    { key: 'reviewing', label: '材料审核', status: 'current', time: '' },
    { key: 'contract', label: '合同签署', status: 'pending', time: '' },
    { key: 'completed', label: '入住确认', status: 'pending', time: '' },
  ],
})

// 加载状态
const loading = ref(false)

// 组件引用
const applicationFormRef = ref()

// 标签切换
function onTabClick(index) {
  tabIndex.value = index
}

// 处理表单提交
async function handleFormSubmit(formData) {
  try {
    loading.value = true

    // 模拟提交API调用
    await new Promise(resolve => setTimeout(resolve, 2000))

    await showToast({
      title: '申请提交成功',
      icon: 'success',
    })

    // 切换到进度页面
    tabIndex.value = 1

    // 这里可以更新进度数据
    // applicationProgress.value = { ... }
  }
  catch (error) {
    await showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 处理取消申请
async function handleCancelApplication() {
  await showToast({
    title: '申请已取消',
    icon: 'success',
  })

  // 重置表单并切换到申请页面
  applicationFormRef.value?.resetForm()
  tabIndex.value = 0
}

// 处理查看申请详情
function handleViewDetails() {
  showToast({
    title: '功能开发中',
    icon: 'none',
  })
}
</script>

<template>
  <view class="h-full flex flex-col overflow-hidden">
    <!-- 顶部安全区域 -->
    <view class="h-[--safe-top] flex-none"></view>

    <!-- 标签切换 -->
    <view class="flex flex-none bg-white mx-4 mt-5 rounded-full shadow-sm overflow-hidden !uni-mp:mr-[--safe-right]">
      <view
        v-for="(item, index) of tabModel"
        :key="index"
        class="h-10 w-0 flex flex-1 items-center justify-center transition-all duration-200"
        :class="[
          tabIndex === index
            ? 'bg-primary-500 text-white font-bold'
            : 'text-gray-600 active:bg-gray-50',
        ]"
        @click="onTabClick(index)"
      >
        {{ item.label }}
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="mt-4 h-0 flex-1 overflow-auto">
      <!-- 入住申请页面 -->
      <ApplicationForm
        v-if="tabIndex === 0"
        ref="applicationFormRef"
        :loading="loading"
        @submit="handleFormSubmit"
      />

      <!-- 申请进度页面 -->
      <ApplicationProgress
        v-else
        :progress-data="applicationProgress"
        @cancel="handleCancelApplication"
        @view-details="handleViewDetails"
      />
    </view>
  </view>
</template>

<script setup>
import { showToast } from '@uni-helper/uni-promises'

// 定义组件的 props 和 emits
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['submit'])

// 表单数据
const formModel = reactive({
  applicantName: '',
  idCard: '',
  phone: '',
  remark: '',
  moveInDate: '',
  roomType: '',
})

// 文件上传数据
const fileList = ref([])
const educationFileList = ref([])

// 户型选择数据
const roomTypeOptions = [
  { label: '单人间', value: 'single' },
  { label: '双人间', value: 'double' },
  { label: '三人间', value: 'triple' },
  { label: '四人间', value: 'quad' },
]

// 表单验证规则
const formRules = {
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' },
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    {
      validator: (value) => {
        const pattern = /^(?:\d{15}|\d{17}[\dX])$/i
        if (value && !pattern.test(value)) {
          return Promise.reject(new Error('身份证号格式不正确'))
        }
        return Promise.resolve()
      },
      trigger: 'blur',
    },
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    {
      validator: (value) => {
        const pattern = /^1[3-9]\d{9}$/
        if (value && !pattern.test(value)) {
          return Promise.reject(new Error('手机号格式不正确'))
        }
        return Promise.resolve()
      },
      trigger: 'blur',
    },
  ],
  moveInDate: [
    { required: true, message: '请选择预计入住日期', trigger: 'change' },
  ],
  roomType: [
    { required: true, message: '请选择户型', trigger: 'change' },
  ],
}

// 表单引用
const formRef = ref()

// 日期选择
function onDateChange(event) {
  formModel.moveInDate = event.value
}

// 户型选择
function onRoomTypeChange(event) {
  formModel.roomType = event.value
}

// 文件上传处理
function handleFileChange(event) {
  fileList.value = event.fileList
}

function handleEducationFileChange(event) {
  educationFileList.value = event.fileList
}

// 提交表单
async function handleSubmit() {
  try {
    // 表单验证
    const { valid } = await formRef.value.validate()

    if (!valid) {
      return
    }

    // 验证文件上传
    if (fileList.value.length === 0) {
      await showToast({
        title: '请上传入职证明',
        icon: 'none',
      })
      return
    }

    // 提交数据
    const submitData = {
      ...formModel,
      jobProofFiles: fileList.value,
      educationFiles: educationFileList.value,
    }

    emit('submit', submitData)
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
function resetForm() {
  formRef.value?.resetFields()
  fileList.value = []
  educationFileList.value = []
}

// 暴露方法给父组件
defineExpose({
  resetForm,
})
</script>

<template>
  <view class="application-form px-4 pb-8">
    <wd-form ref="formRef" :model="formModel" :rules="formRules">
      <!-- 基本信息 -->
      <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
        <view class="px-4 py-3 border-b border-gray-100">
          <view class="text-lg font-bold text-gray-800">
            基本信息
          </view>
          <view class="text-sm text-gray-500 mt-1">
            请填写真实有效的个人信息
          </view>
        </view>

        <wd-cell-group>
          <wd-input
            v-model="formModel.applicantName"
            prop="applicantName"
            label="申请人"
            label-width="80px"
            placeholder="请输入申请人姓名"
            clearable
            :maxlength="20"
            required
          />

          <wd-input
            v-model="formModel.idCard"
            prop="idCard"
            label="身份证号"
            label-width="80px"
            placeholder="请输入身份证号"
            clearable
            :maxlength="18"
            required
          />

          <wd-input
            v-model="formModel.phone"
            prop="phone"
            label="联系电话"
            label-width="80px"
            placeholder="请输入联系电话"
            type="number"
            clearable
            :maxlength="11"
            required
          />

          <wd-textarea
            v-model="formModel.remark"
            rop="remark"
            label="申请备注"
            label-width="80px"
            placeholder="请输入申请备注（选填）"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>
      </view>

      <!-- 入住信息 -->
      <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
        <view class="px-4 py-3 border-b border-gray-100">
          <view class="text-lg font-bold text-gray-800">
            入住信息
          </view>
          <view class="text-sm text-gray-500 mt-1">
            请选择您的入住偏好
          </view>
        </view>

        <wd-cell-group>
          <wd-datetime-picker
            v-model="formModel.moveInDate"
            prop="moveInDate"
            label="预计入住日期"
            label-width="100px"
            type="date"
            placeholder="请选择预计入住日期"
            required
            @confirm="onDateChange"
          />

          <wd-select-picker
            v-model="formModel.roomType"
            prop="roomType"
            label="户型选择"
            label-width="80px"
            :columns="roomTypeOptions"
            placeholder="请选择户型"
            required
            @confirm="onRoomTypeChange"
          />
        </wd-cell-group>
      </view>

      <!-- 材料上传 -->
      <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
        <view class="px-4 py-3 border-b border-gray-100">
          <view class="text-lg font-bold text-gray-800">
            材料上传
          </view>
          <view class="text-sm text-gray-500 mt-1">
            请上传清晰的证明材料照片
          </view>
        </view>

        <view class="p-4 space-y-6">
          <!-- 入职证明 -->
          <view class="space-y-3">
            <view class="flex items-center text-sm font-medium text-gray-700">
              <text>入职证明</text>
              <text class="text-red-500 ml-1">
                *
              </text>
            </view>
            <view class="text-xs text-gray-500 mb-2">
              支持 JPG、PNG 格式，单个文件不超过 5MB
            </view>
            <wd-upload
              :file-list="fileList"
              action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
              :limit="3"
              multiple
              @change="handleFileChange"
            />
          </view>

          <!-- 学历证明 -->
          <view class="space-y-3">
            <view class="text-sm font-medium text-gray-700">
              学历证明
            </view>
            <view class="text-xs text-gray-500 mb-2">
              支持 JPG、PNG 格式，单个文件不超过 5MB（选填）
            </view>
            <wd-upload
              :file-list="educationFileList"
              action="https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload"
              :limit="3"
              multiple
              @change="handleEducationFileChange"
            />
          </view>
        </view>
      </view>
    </wd-form>

    <!-- 提交按钮 -->
    <view class="px-2">
      <wd-button
        type="primary"
        size="large"
        block
        :loading="loading"
        @click="handleSubmit"
      >
        {{ loading ? '提交中...' : '提交申请' }}
      </wd-button>
    </view>
  </view>
</template>

<style>
</style>

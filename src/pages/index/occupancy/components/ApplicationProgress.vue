<script setup>
import { showModal, showToast } from '@uni-helper/uni-promises'

// 定义组件的 props 和 emits
const props = defineProps({
  progressData: {
    type: Object,
    default: () => ({
      applicationId: 'AP202501080001',
      status: 'reviewing',
      submitTime: '2025-01-08 14:30:00',
      steps: [
        { key: 'submitted', label: '申请提交', status: 'completed', time: '2025-01-08 14:30:00' },
        { key: 'reviewing', label: '材料审核', status: 'current', time: '' },
        { key: 'contract', label: '合同签署', status: 'pending', time: '' },
        { key: 'completed', label: '入住确认', status: 'pending', time: '' },
      ],
    }),
  },
})

const emit = defineEmits(['cancel', 'view-details'])

// 获取当前步骤索引（用于 wd-steps 的 active 属性）
const activeStepIndex = computed(() => {
  const currentIndex = props.progressData.steps.findIndex(step => step.status === 'current')
  return currentIndex >= 0 ? currentIndex : 0
})

// 获取当前状态描述
const currentStatusText = computed(() => {
  const currentStep = props.progressData.steps.find(step => step.status === 'current')
  if (!currentStep)
    return '处理中'

  switch (currentStep.key) {
    case 'submitted':
      return '申请已提交，等待审核'
    case 'reviewing':
      return '材料审核中'
    case 'contract':
      return '等待签署合同'
    case 'completed':
      return '等待入住确认'
    default:
      return '处理中'
  }
})

// 将步骤状态映射为 wd-steps 支持的状态
function mapStepStatus(status) {
  switch (status) {
    case 'completed':
      return 'finished'
    case 'current':
      return 'process'
    default:
      return undefined // pending 状态不设置，使用默认样式
  }
}

// 获取步骤说明文本
function getStepDescription(step) {
  if (step.status !== 'current')
    return ''

  switch (step.key) {
    case 'submitted':
      return '您的申请已成功提交，我们将尽快处理'
    case 'reviewing':
      return '正在审核您提交的材料，请耐心等待'
    case 'contract':
      return '材料审核通过，请准备签署入住合同'
    case 'completed':
      return '合同已签署，请按约定时间办理入住手续'
    default:
      return ''
  }
}

// 取消申请
async function handleCancel() {
  try {
    const result = await showModal({
      title: '确认取消',
      content: '确定要取消此申请吗？取消后无法恢复。',
      confirmText: '确定取消',
      cancelText: '我再想想',
    })

    if (result.confirm) {
      emit('cancel')
    }
  }
  catch (error) {
    console.log('用户取消操作')
  }
}

// 查看申请详情
function handleViewDetails() {
  emit('view-details')
}
</script>

<template>
  <view class="application-progress px-4 pb-8">
    <!-- 申请信息 -->
    <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-bold text-gray-800">
          申请信息
        </view>
      </view>

      <view class="p-4 space-y-3">
        <view class="flex justify-between items-center">
          <text class="text-gray-600">
            申请单号
          </text>
          <text class="font-medium text-gray-800">
            {{ progressData.applicationId }}
          </text>
        </view>
        <view class="flex justify-between items-center">
          <text class="text-gray-600">
            申请时间
          </text>
          <text class="font-medium text-gray-800">
            {{ progressData.submitTime }}
          </text>
        </view>
      </view>
    </view>

    <!-- 进度展示 -->
    <view class="bg-white rounded-xl shadow-sm overflow-hidden mb-6">
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-bold text-gray-800">
          办理进度
        </view>
        <view class="text-sm text-gray-500 mt-1">
          当前状态：{{ currentStatusText }}
        </view>
      </view>

      <view class="p-4">
        <!-- 使用 wd-steps 组件 -->
        <wd-steps :active="activeStepIndex" vertical>
          <wd-step
            v-for="step in progressData.steps"
            :key="step.key"
            :title="step.label"
            :status="mapStepStatus(step.status)"
          >
            <!-- 自定义步骤内容 -->
            <template #description>
              <view class="space-y-2">
                <!-- 时间信息 -->
                <view
                  v-if="step.time"
                  class="text-xs text-gray-500"
                >
                  {{ step.time }}
                </view>

                <!-- 当前步骤的详细说明 -->
                <view
                  v-if="step.status === 'current' && getStepDescription(step)"
                  class="text-sm text-gray-600"
                >
                  {{ getStepDescription(step) }}
                </view>
              </view>
            </template>
          </wd-step>
        </wd-steps>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="space-y-3">
      <!-- <wd-button
        type="primary"
        size="large"
        block
        @click="handleViewDetails"
      >
        查看申请详情
      </wd-button> -->

      <wd-button
        type="error"
        size="large"
        block
        plain
        @click="handleCancel"
      >
        取消申请
      </wd-button>
    </view>
  </view>
</template>

<style>
</style>
